#!/usr/bin/env node

/**
 * Seed Notification Test Data Script
 * Creates certificates with various expiry dates to test the notification system
 *
 * Usage:
 *   node scripts/seed-notification-test-data.js
 *   pnpm seed:notification-test
 */

// Load environment variables
require("dotenv").config({ path: ".env.local" });

const { neon } = require("@neondatabase/serverless");
const { drizzle } = require("drizzle-orm/neon-http");
const { eq } = require("drizzle-orm");
const { pgTable, text, timestamp, boolean } = require("drizzle-orm/pg-core");

// Use dynamic import for nanoid
async function getNanoid() {
  const { nanoid } = await import("nanoid");
  return nanoid;
}

// Define schema tables for seeding
const users = pgTable("User", {
  id: text("id").primaryKey(),
  email: text("email").notNull().unique(),
});

const certificates = pgTable("Certificate", {
  id: text("id").primaryKey(),
  name: text("name").notNull(),
  issuingAuthority: text("issuingAuthority").notNull(),
  certificateNumber: text("certificateNumber").notNull(),
  dateIssued: timestamp("dateIssued").notNull(),
  expiryDate: timestamp("expiryDate"),
  notes: text("notes"),
  isFavorite: boolean("isFavorite").default(false).notNull(),
  userId: text("userId").notNull(),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

const notifications = pgTable("Notification", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull(),
  type: text("type").notNull(),
  title: text("title").notNull(),
  message: text("message").notNull(),
  read: boolean("read").default(false).notNull(),
  actionUrl: text("actionUrl"),
  metadata: text("metadata"),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  expiresAt: timestamp("expiresAt"),
});

async function seedNotificationTestData() {
  // Only run in development or test environments
  if (process.env.NODE_ENV === "production") {
    console.log("❌ Notification test data seeding is disabled in production");
    process.exit(1);
  }

  if (!process.env.DATABASE_URL) {
    console.log("❌ DATABASE_URL environment variable is required");
    process.exit(1);
  }

  console.log("🔔 Seeding notification test data...");

  try {
    const nanoid = await getNanoid();
    const sql = neon(process.env.DATABASE_URL);
    const db = drizzle(sql);

    const testEmail = "<EMAIL>";

    // Get the test user
    const testUser = await db
      .select()
      .from(users)
      .where(eq(users.email, testEmail))
      .limit(1);

    if (testUser.length === 0) {
      console.log(
        "❌ Test user not found. Please run 'pnpm seed:test-user' first"
      );
      process.exit(1);
    }

    const testUserId = testUser[0].id;
    console.log(`👤 Found test user: ${testEmail} (${testUserId})`);

    // Clear existing notifications for test user
    console.log("🧹 Clearing existing notifications...");
    await db.delete(notifications).where(eq(notifications.userId, testUserId));

    // Create certificates with different expiry dates to trigger notifications
    const today = new Date();
    const testCertificates = [
      {
        id: nanoid(),
        name: "Certificate Expiring in 7 Days",
        issuingAuthority: "Test Maritime Authority",
        certificateNumber: "TEST-7D-001",
        dateIssued: new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000), // 1 year ago
        expiryDate: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        notes:
          "This certificate will expire in 7 days - should trigger notification",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: nanoid(),
        name: "Certificate Expiring in 30 Days",
        issuingAuthority: "Test Maritime Authority",
        certificateNumber: "TEST-30D-001",
        dateIssued: new Date(today.getTime() - 335 * 24 * 60 * 60 * 1000), // ~11 months ago
        expiryDate: new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        notes:
          "This certificate will expire in 30 days - should trigger notification",
        isFavorite: true,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: nanoid(),
        name: "Certificate Expiring in 60 Days",
        issuingAuthority: "Test Maritime Authority",
        certificateNumber: "TEST-60D-001",
        dateIssued: new Date(today.getTime() - 305 * 24 * 60 * 60 * 1000), // ~10 months ago
        expiryDate: new Date(today.getTime() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        notes:
          "This certificate will expire in 60 days - should trigger notification",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: nanoid(),
        name: "Certificate Expiring in 90 Days",
        issuingAuthority: "Test Maritime Authority",
        certificateNumber: "TEST-90D-001",
        dateIssued: new Date(today.getTime() - 275 * 24 * 60 * 60 * 1000), // ~9 months ago
        expiryDate: new Date(today.getTime() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
        notes:
          "This certificate will expire in 90 days - should trigger notification",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: nanoid(),
        name: "Certificate Expiring Today",
        issuingAuthority: "Test Maritime Authority",
        certificateNumber: "TEST-0D-001",
        dateIssued: new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000), // 1 year ago
        expiryDate: new Date(today.getTime() + 12 * 60 * 60 * 1000), // 12 hours from now (today)
        notes:
          "This certificate expires today - should trigger urgent notification",
        isFavorite: true,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: nanoid(),
        name: "Certificate Valid for 2 Years",
        issuingAuthority: "Test Maritime Authority",
        certificateNumber: "TEST-VALID-001",
        dateIssued: new Date(),
        expiryDate: new Date(today.getTime() + 2 * 365 * 24 * 60 * 60 * 1000), // 2 years from now
        notes:
          "This certificate is valid for a long time - should not trigger notifications",
        isFavorite: false,
        userId: testUserId,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    // Delete existing test certificates first
    console.log("🧹 Clearing existing test certificates...");
    await db.delete(certificates).where(eq(certificates.userId, testUserId));

    // Insert new test certificates
    console.log("📜 Creating test certificates with various expiry dates...");
    await db.insert(certificates).values(testCertificates);

    // Create some sample notifications to test the UI
    console.log("🔔 Creating sample notifications...");
    // MOCK DATA: Simple notification examples for development testing
    const sampleNotifications = [
      {
        id: nanoid(),
        userId: testUserId,
        type: "certificate_expiry",
        title: "Certificate Expiring Soon",
        message:
          "Your Test Certificate will expire in 7 days. Please renew it to avoid any issues.",
        read: false,
        actionUrl: "/certificates",
        metadata: JSON.stringify({
          certificateId: testCertificates[0].id,
          certificateName: testCertificates[0].name,
          expiryDate: testCertificates[0].expiryDate.toISOString(),
          daysUntilExpiry: 7,
        }),
        createdAt: new Date(today.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
        expiresAt: null,
      },
      {
        id: nanoid(),
        userId: testUserId,
        type: "system",
        title: "Welcome to Sailor Plus",
        message:
          "Welcome to the Sailor Plus maritime certificate management platform. Start by adding your certificates.",
        read: true,
        actionUrl: "/certificates",
        metadata: null,
        createdAt: new Date(today.getTime() - 24 * 60 * 60 * 1000), // 1 day ago
        expiresAt: null,
      },
      {
        id: nanoid(),
        userId: testUserId,
        type: "reminder",
        title: "Profile Incomplete",
        message:
          "Complete your maritime profile to get personalized certificate recommendations.",
        read: false,
        actionUrl: "/profile",
        metadata: null,
        createdAt: new Date(today.getTime() - 6 * 60 * 60 * 1000), // 6 hours ago
        expiresAt: null,
      },
    ];

    await db.insert(notifications).values(sampleNotifications);

    console.log("✅ Notification test data seeded successfully!");
    console.log("");
    console.log("📊 Test Data Summary:");
    console.log(`   📜 Created ${testCertificates.length} test certificates`);
    console.log(
      `   🔔 Created ${sampleNotifications.length} sample notifications`
    );
    console.log("");
    console.log("📅 Certificate Expiry Schedule:");
    console.log("   🔴 1 certificate expires today");
    console.log("   🟠 1 certificate expires in 7 days");
    console.log("   🟡 1 certificate expires in 30 days");
    console.log("   🟢 1 certificate expires in 60 days");
    console.log("   🔵 1 certificate expires in 90 days");
    console.log("   ✅ 1 certificate valid for 2 years");
    console.log("");
    console.log("🧪 Test the notification system:");
    console.log("   1. <NAME_EMAIL>");
    console.log("   2. Check notification bell in topbar");
    console.log("   3. Visit /notifications page");
    console.log("   4. Test the expiry check cron job");
  } catch (error) {
    console.error("❌ Error seeding notification test data:", error);
    process.exit(1);
  }
}

// Run the seeding
seedNotificationTestData();

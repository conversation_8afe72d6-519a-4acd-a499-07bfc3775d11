"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useEffect, useState } from "react";

export default function DebugPage() {
  const [authStatus, setAuthStatus] = useState<string>("checking");
  const [certificates, setCertificates] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [sessionInfo, setSessionInfo] = useState<any>(null);

  const checkAuth = async () => {
    try {
      setAuthStatus("checking");
      setError(null);

      // Test API call
      const response = await fetch("/api/certificates", {
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setCertificates(data);
        setAuthStatus("authenticated");
        console.log("Auth check successful:", data.length, "certificates");
      } else {
        setAuthStatus("unauthenticated");
        const errorText = await response.text();
        setError(`${response.status}: ${errorText}`);
        console.log("Auth check failed:", response.status, errorText);
      }
    } catch (err) {
      setAuthStatus("error");
      setError(err instanceof Error ? err.message : "Unknown error");
      console.error("Auth check error:", err);
    }
  };

  const testLogin = async () => {
    try {
      const response = await fetch("/api/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: "<EMAIL>",
          password: "demo123",
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSessionInfo(data.user);
        console.log("Login successful:", data);
        // Recheck auth after login
        setTimeout(checkAuth, 100);
      } else {
        const errorText = await response.text();
        setError(`Login failed: ${errorText}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Login error");
    }
  };

  const testLogout = async () => {
    try {
      const response = await fetch("/api/logout", {
        method: "POST",
        credentials: "include",
      });

      if (response.ok) {
        setSessionInfo(null);
        setCertificates([]);
        console.log("Logout successful");
        // Recheck auth after logout
        setTimeout(checkAuth, 100);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Logout error");
    }
  };

  useEffect(() => {
    checkAuth();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "authenticated":
        return "bg-green-500";
      case "unauthenticated":
        return "bg-red-500";
      case "checking":
        return "bg-yellow-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Authentication Debug</h1>
        <Badge className={getStatusColor(authStatus)}>
          {authStatus.toUpperCase()}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Authentication Status */}
        <Card>
          <CardHeader>
            <CardTitle>Authentication Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <strong>Status:</strong> {authStatus}
            </div>
            {sessionInfo && (
              <div>
                <strong>User:</strong>
                <pre className="bg-gray-100 p-2 rounded text-sm mt-1">
                  {JSON.stringify(sessionInfo, null, 2)}
                </pre>
              </div>
            )}
            {error && (
              <div className="text-red-600">
                <strong>Error:</strong> {error}
              </div>
            )}
            <div className="flex gap-2">
              <Button onClick={checkAuth} variant="outline" size="sm">
                Recheck Auth
              </Button>
              <Button onClick={testLogin} variant="outline" size="sm">
                Test Login
              </Button>
              <Button onClick={testLogout} variant="outline" size="sm">
                Test Logout
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Certificates Data */}
        <Card>
          <CardHeader>
            <CardTitle>Certificates Data</CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <strong>Count:</strong> {certificates.length}
            </div>
            {certificates.length > 0 && (
              <div className="mt-4">
                <strong>Sample Certificates:</strong>
                <div className="space-y-2 mt-2">
                  {certificates.slice(0, 3).map((cert, index) => (
                    <div
                      key={index}
                      className="bg-gray-100 p-2 rounded text-sm"
                    >
                      <div>
                        <strong>Name:</strong> {cert.name}
                      </div>
                      <div>
                        <strong>Favorite:</strong>{" "}
                        {cert.isFavorite ? "Yes" : "No"}
                      </div>
                      <div>
                        <strong>Expiry:</strong>{" "}
                        {cert.expiryDate || "No expiry"}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Cookie Information */}
      <Card>
        <CardHeader>
          <CardTitle>Browser Cookie Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm">
            <strong>Note:</strong> Session cookies are HTTP-only and cannot be
            read by JavaScript. This is a security feature. The browser
            automatically sends them with requests.
          </div>
          <div className="mt-2 text-sm text-gray-600">
            If authentication is working, you should see certificates data
            above.
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div>
            1. Click "Test Login" to <NAME_EMAIL>
          </div>
          <div>2. Status should change to "AUTHENTICATED"</div>
          <div>3. You should see certificates data populated</div>
          <div>4. Click "Test Logout" to clear the session</div>
          <div>5. Status should change to "UNAUTHENTICATED"</div>
        </CardContent>
      </Card>
    </div>
  );
}

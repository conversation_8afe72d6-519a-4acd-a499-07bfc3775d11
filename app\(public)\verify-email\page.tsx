"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle, Loader2, Mail, RefreshCw, XCircle } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

interface VerificationState {
  status: "loading" | "success" | "error" | "expired" | "already-verified";
  message: string;
  userEmail?: string;
  userName?: string;
}

function VerifyEmailForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const [state, setState] = useState<VerificationState>({
    status: "loading",
    message: "Verifying your email address...",
  });
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    if (!token) {
      setState({
        status: "error",
        message:
          "Invalid verification link. Please check your email for the correct link.",
      });
      return;
    }

    verifyEmail(token);
  }, [token]);

  const verifyEmail = async (verificationToken: string) => {
    try {
      setState({
        status: "loading",
        message: "Verifying your email address...",
      });

      const response = await fetch("/api/auth/verify-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token: verificationToken }),
      });

      const data = await response.json();

      if (data.success) {
        if (data.alreadyVerified) {
          setState({
            status: "already-verified",
            message:
              "Your email address has already been verified. You can access your maritime certification dashboard.",
            userEmail: data.userEmail,
          });
        } else {
          setState({
            status: "success",
            message:
              "Email verified successfully! Your account is now active. Redirecting to your dashboard...",
            userEmail: data.userEmail,
          });

          // For newly verified users, automatically redirect to dashboard after a shorter delay
          if (data.shouldRedirect) {
            setTimeout(() => {
              window.location.href = data.redirectUrl || "/dashboard";
            }, 1500); // Reduced from 2000ms to 1500ms for better UX
          }
        }
      } else {
        if (data.error.includes("expired")) {
          setState({
            status: "expired",
            message:
              "Your verification link has expired. Please request a new one.",
            userEmail: data.userEmail,
          });
        } else {
          setState({
            status: "error",
            message: data.error || "Failed to verify email address.",
          });
        }
      }
    } catch (error) {
      console.error("Verification error:", error);
      setState({
        status: "error",
        message: "Something went wrong. Please try again.",
      });
    }
  };

  const handleResendVerification = async () => {
    if (!state.userEmail) {
      setState({
        ...state,
        message:
          "Unable to resend verification email. Please try signing up again.",
      });
      return;
    }

    setIsResending(true);
    try {
      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: state.userEmail }),
      });

      const data = await response.json();

      if (data.success) {
        setState({
          status: "success",
          message: "New verification email sent! Please check your inbox.",
          userEmail: state.userEmail,
        });
      } else {
        setState({
          ...state,
          message: data.error || "Failed to resend verification email.",
        });
      }
    } catch (error) {
      console.error("Resend error:", error);
      setState({
        ...state,
        message: "Failed to resend verification email. Please try again.",
      });
    } finally {
      setIsResending(false);
    }
  };

  const getIcon = () => {
    switch (state.status) {
      case "loading":
        return <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />;
      case "success":
      case "already-verified":
        return <CheckCircle className="h-12 w-12 text-green-500" />;
      case "error":
      case "expired":
        return <XCircle className="h-12 w-12 text-red-500" />;
      default:
        return <Mail className="h-12 w-12 text-gray-500" />;
    }
  };

  const getTitle = () => {
    switch (state.status) {
      case "loading":
        return "Verifying Email...";
      case "success":
        return "Email Verified!";
      case "already-verified":
        return "Already Verified";
      case "expired":
        return "Link Expired";
      case "error":
        return "Verification Failed";
      default:
        return "Email Verification";
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">⚓ Sailor Plus</h1>
          <p className="mt-2 text-sm text-gray-600">
            Maritime Certification Platform
          </p>
        </div>

        <Card>
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">{getIcon()}</div>
            <CardTitle className="text-xl">{getTitle()}</CardTitle>
            <CardDescription>
              {state.userEmail && (
                <span className="block text-sm text-gray-600 mt-2">
                  {state.userEmail}
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert
              variant={
                state.status === "success" ||
                state.status === "already-verified"
                  ? "default"
                  : "destructive"
              }
            >
              <AlertDescription>{state.message}</AlertDescription>
            </Alert>

            <div className="space-y-3">
              {(state.status === "success" ||
                state.status === "already-verified") && (
                <Button
                  onClick={() => {
                    // Force a page reload to ensure session is properly updated
                    window.location.href = "/dashboard";
                  }}
                  className="w-full"
                >
                  Continue to Dashboard
                </Button>
              )}

              {state.status === "expired" && (
                <Button
                  onClick={handleResendVerification}
                  disabled={isResending}
                  className="w-full"
                >
                  {isResending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Send New Verification Email
                    </>
                  )}
                </Button>
              )}

              {state.status === "error" && (
                <div className="space-y-2">
                  <Button
                    onClick={() => router.push("/signup")}
                    variant="outline"
                    className="w-full"
                  >
                    Back to Sign Up
                  </Button>
                  <Button
                    onClick={() => router.push("/login")}
                    className="w-full"
                  >
                    Go to Login
                  </Button>
                </div>
              )}

              {state.status !== "loading" && (
                <Button
                  onClick={() => router.push("/")}
                  variant="ghost"
                  className="w-full"
                >
                  Back to Home
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-sm text-gray-500">
          <p>Need help? Contact our support team</p>
        </div>
      </div>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            <div className="text-center">
              <h1 className="text-3xl font-bold text-gray-900">
                ⚓ Sailor Plus
              </h1>
              <p className="mt-2 text-sm text-gray-600">
                Maritime Certification Platform
              </p>
            </div>
            <Card>
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <Loader2 className="h-12 w-12 text-blue-500 animate-spin" />
                </div>
                <CardTitle className="text-xl">Loading...</CardTitle>
              </CardHeader>
            </Card>
          </div>
        </div>
      }
    >
      <VerifyEmailForm />
    </Suspense>
  );
}

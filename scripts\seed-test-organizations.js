#!/usr/bin/env node

/**
 * Script to seed test organizations for admin dashboard testing
 */

const { neon } = require("@neondatabase/serverless");
const dotenv = require("dotenv");

// Simple ID generator
function generateId() {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}

// Load environment variables
dotenv.config({ path: ".env.local" });

async function seedTestOrganizations() {
  console.log("🚀 Seeding test organizations...");
  console.log("📅 Date:", new Date().toISOString());

  if (!process.env.DATABASE_URL) {
    console.error("❌ DATABASE_URL environment variable is required");
    process.exit(1);
  }

  const sql = neon(process.env.DATABASE_URL);

  try {
    // MOCK DATA: Create simple test organizations for development
    const organizations = [
      {
        id: generateId(),
        name: "Ocean Explorer Yachts",
        type: "yacht_company",
        status: "verified",
        contactEmail: "<EMAIL>",
        description: "Test yacht company for development",
        website: "https://oceanexplorer.com",
        verifiedAt: new Date(),
        verifiedBy: "<EMAIL>",
      },
      {
        id: generateId(),
        name: "Maritime Training Institute",
        type: "cert_provider",
        status: "verified",
        contactEmail: "<EMAIL>",
        description: "Test training provider for development",
        website: "https://maritimetraining.edu",
        verifiedAt: new Date(),
        verifiedBy: "<EMAIL>",
      },
    ];

    console.log("📝 Creating test organizations...");
    for (const org of organizations) {
      await sql`
        INSERT INTO "Organization" (
          id, name, type, status, "contactEmail", description, website,
          "verifiedAt", "verifiedBy", "createdAt", "updatedAt"
        ) VALUES (
          ${org.id}, ${org.name}, ${org.type}, ${org.status}, ${
        org.contactEmail
      },
          ${org.description}, ${org.website}, ${org.verifiedAt || null},
          ${org.verifiedBy || null}, NOW(), NOW()
        )
        ON CONFLICT (id) DO NOTHING
      `;
      console.log(`✅ Created organization: ${org.name} (${org.status})`);
    }

    // Get admin user ID for membership creation
    const adminUsers = await sql`
      SELECT id FROM "User" WHERE email = '<EMAIL>' LIMIT 1
    `;

    if (adminUsers.length > 0) {
      const adminUserId = adminUsers[0].id;

      // Create some organization memberships
      console.log("📝 Creating organization memberships...");

      // Add admin as owner of first organization
      await sql`
        INSERT INTO "OrganizationMembership" (
          id, "userId", "organizationId", role, "joinedAt", "acceptedAt", status
        ) VALUES (
          ${generateId()}, ${adminUserId}, ${
        organizations[0].id
      }, 'owner', NOW(), NOW(), 'active'
        )
        ON CONFLICT ("userId", "organizationId") DO NOTHING
      `;
      console.log(`✅ Added admin as owner of ${organizations[0].name}`);
    }

    console.log("🎉 Test organizations seeded successfully!");
    console.log("");
    console.log("📋 Seeding Summary:");
    console.log(`   ✅ Created ${organizations.length} test organizations`);
    console.log("   ✅ 1 yacht company, 1 certification provider");
    console.log("");
    console.log("🔍 Verifying organizations...");

    // Verify organizations were created
    const createdOrgs = await sql`
      SELECT name, type, status FROM "Organization" ORDER BY "createdAt" DESC LIMIT 10
    `;

    console.log("📋 Recent organizations:");
    createdOrgs.forEach((org, index) => {
      console.log(`   ${index + 1}. ${org.name} (${org.type}, ${org.status})`);
    });
  } catch (error) {
    console.error("❌ Seeding failed:", error);
    process.exit(1);
  }
}

// Run seeding
seedTestOrganizations();

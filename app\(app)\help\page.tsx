"use client";

import type React from "react";

import {
  FileQuestion,
  LifeBuoy,
  Mail,
  MessageSquare,
  Phone,
  Search,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

export default function HelpPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock FAQ data
  const faqs = [
    {
      question: "How do I add a new certificate?",
      answer:
        "To add a new certificate, click on the 'Add Certificate' button on the dashboard or certificates page. Fill in the required details such as certificate name, issuing authority, certificate number, and expiry date. You can also upload a digital copy of your certificate for safekeeping.",
    },
    {
      question: "How do I get notified about expiring certificates?",
      answer:
        "Sailor Plus automatically tracks the expiry dates of your certificates and displays them on the main Certificates page. You can view expiring certificates by clicking the 'Expiring Soon' filter card, which shows certificates expiring in the next 90 days. You can configure notification settings in the Settings page under the Notifications tab.",
    },
    {
      question: "Can I export my certificates?",
      answer:
        "Yes, you can export your certificates in various formats. On the Certificates page, click the 'Export' button and select your preferred format (PDF, CSV, or Excel). You can export all certificates or filter them before exporting.",
    },
    {
      question: "How do I update my profile information?",
      answer:
        "To update your profile information, go to the Profile page by clicking on your avatar in the top right corner and selecting 'My Profile'. You can edit your personal details, update your profile picture, and change your password.",
    },
    {
      question: "Is my certificate data secure?",
      answer:
        "Yes, Sailor Plus takes data security seriously. All your certificate data is encrypted and stored securely. We use industry-standard security measures to protect your information. You can review our privacy policy for more details on how we handle your data.",
    },
  ];

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch("/api/certificates", {
          credentials: "include",
        });

        if (!response.ok) {
          if (response.status === 401) {
            router.push("/login");
            return;
          }
        }

        // Simulate loading help content
        setTimeout(() => {
          setIsLoading(false);
        }, 500);
      } catch (error) {
        console.error("Auth check failed:", error);
        router.push("/login");
      }
    };

    checkAuth();
  }, [router]);

  const filteredFaqs = searchQuery
    ? faqs.filter(
        (faq) =>
          faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
          faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : faqs;

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      // Reset form
      const form = e.target as HTMLFormElement;
      form.reset();
    }, 1500);
  };

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-pulse">Loading help center...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="mt-4 text-center">
          <Button onClick={() => window.location.reload()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 md:p-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Help & Support</h1>
        <p className="text-muted-foreground">
          Find answers to common questions or contact our support team
        </p>
      </div>

      <div className="relative w-full max-w-lg mx-auto">
        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search for help topics..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <Tabs defaultValue="faq">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="faq">
            <FileQuestion className="mr-2 h-4 w-4" /> FAQ
          </TabsTrigger>
          <TabsTrigger value="contact">
            <MessageSquare className="mr-2 h-4 w-4" /> Contact Us
          </TabsTrigger>
          <TabsTrigger value="support">
            <LifeBuoy className="mr-2 h-4 w-4" /> Support Options
          </TabsTrigger>
        </TabsList>
        <TabsContent value="faq" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>
                Find answers to common questions about Sailor Plus
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredFaqs.length > 0 ? (
                <Accordion type="single" collapsible className="w-full">
                  {filteredFaqs.map((faq, index) => (
                    <AccordionItem key={index} value={`item-${index}`}>
                      <AccordionTrigger>{faq.question}</AccordionTrigger>
                      <AccordionContent>{faq.answer}</AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              ) : (
                <div className="text-center py-8">
                  <FileQuestion className="mx-auto h-12 w-12 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-medium">No results found</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    We couldn't find any FAQs matching your search. Try
                    different keywords or contact support.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="contact" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Contact Support</CardTitle>
              <CardDescription>
                Send us a message and we'll get back to you as soon as possible
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleContactSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="name" className="text-sm font-medium">
                      Name
                    </label>
                    <Input id="name" name="name" required />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium">
                      Email
                    </label>
                    <Input id="email" name="email" type="email" required />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="subject" className="text-sm font-medium">
                    Subject
                  </label>
                  <Input id="subject" name="subject" required />
                </div>
                <div className="space-y-2">
                  <label htmlFor="message" className="text-sm font-medium">
                    Message
                  </label>
                  <Textarea id="message" name="message" rows={5} required />
                </div>
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Sending..." : "Send Message"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="support" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5" /> Phone Support
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>Call our support team for immediate assistance</p>
                <p className="font-medium mt-2">+****************</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Monday - Friday, 9am - 5pm EST
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  Request Callback
                </Button>
              </CardFooter>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" /> Email Support
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>Send us an email and we'll respond within 24 hours</p>
                <p className="font-medium mt-2"><EMAIL></p>
                <p className="text-sm text-muted-foreground mt-1">
                  Available 24/7
                </p>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <a href="mailto:<EMAIL>">Send Email</a>
                </Button>
              </CardFooter>
            </Card>
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" /> Live Chat
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>Chat with our support team in real-time</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Available Monday - Friday, 9am - 8pm EST
                </p>
              </CardContent>
              <CardFooter>
                <Button className="w-full">Start Live Chat</Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

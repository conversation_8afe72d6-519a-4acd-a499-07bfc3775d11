import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Upload } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function LandingPage() {
  // Authentication redirect is now handled by middleware
  // This component only renders for unauthenticated users
  return (
    <>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-b from-nautical-light to-white py-20 md:py-32">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="space-y-4">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl text-nautical-blue">
                Sailor Plus: Your Certificates, Organized & Secure
              </h1>
              <p className="max-w-[600px] text-muted-foreground md:text-xl">
                Effortlessly track, manage, and prepare your vital maritime
                certifications for your next voyage.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button size="lg" asChild>
                  <Link href="/signup">Get Started Free</Link>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <Link href="#features">How It Works</Link>
                </Button>
              </div>
            </div>
            <div className="relative h-[300px] md:h-[400px] lg:h-[500px]">
              <Image
                src="/placeholder.svg?height=500&width=500"
                alt="Sailor Plus Dashboard Preview"
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-nautical-blue">
              Key Features
            </h2>
            <p className="mt-4 text-xl text-muted-foreground">
              Everything you need to manage your maritime certifications
            </p>
          </div>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <FileCheck className="h-10 w-10 text-primary mb-2" />
                <CardTitle>Centralized Certificate Hub</CardTitle>
                <CardDescription>
                  Store all your certificates in one secure location, accessible
                  anytime, anywhere.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Never lose track of important documents again. Sailor Plus
                  keeps everything organized and at your fingertips.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Bell className="h-10 w-10 text-primary mb-2" />
                <CardTitle>Automated Expiry Alerts</CardTitle>
                <CardDescription>
                  Receive timely notifications before your certificates expire.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Stay ahead with customizable alerts. Never miss a renewal
                  deadline and maintain compliance at all times.
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Upload className="h-10 w-10 text-primary mb-2" />
                <CardTitle>Simplified Exporting</CardTitle>
                <CardDescription>
                  Export your certificates in various formats with just a few
                  clicks.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Quickly generate reports and share your certification status
                  with employers, authorities, or crew agencies.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="bg-nautical-light py-16 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="relative h-[300px] md:h-[400px]">
              <Image
                src="/placeholder.svg?height=400&width=400"
                alt="Yacht professional using Sailor Plus"
                fill
                className="object-contain"
              />
            </div>
            <div className="space-y-4">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl text-nautical-blue">
                Focus on Your Voyage, Not Your Paperwork
              </h2>
              <p className="text-muted-foreground md:text-xl">
                Sailor Plus streamlines certificate management so you can spend
                more time doing what you love - being on the water.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start gap-2">
                  <Anchor className="h-5 w-5 text-primary mt-0.5" />
                  <span>Reduce stress with organized documentation</span>
                </li>
                <li className="flex items-start gap-2">
                  <Anchor className="h-5 w-5 text-primary mt-0.5" />
                  <span>
                    Improve employability with ready-to-share credentials
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Anchor className="h-5 w-5 text-primary mt-0.5" />
                  <span>Save time with intuitive certificate management</span>
                </li>
              </ul>
              <div className="pt-4">
                <Button size="lg" asChild>
                  <Link href="/signup">Create Your Sailor Plus</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
